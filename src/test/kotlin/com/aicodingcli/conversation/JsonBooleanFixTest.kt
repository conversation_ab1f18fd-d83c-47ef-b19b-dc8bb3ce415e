package com.aicodingcli.conversation

import com.aicodingcli.ai.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.Assertions.*
import kotlinx.coroutines.runBlocking

class JsonBooleanFixTest {

    private val mockAiService = MockAiService(
        AiServiceConfig(
            provider = AiProvider.MOCK,
            apiKey = "test-key",
            model = "test-model"
        )
    )

    // Custom mock service for testing
    private class CustomMockAiService(
        config: AiServiceConfig,
        private val responseProvider: (AiRequest) -> AiResponse
    ) : AiService {
        override val config: AiServiceConfig = config
        
        override suspend fun chat(request: AiRequest): AiResponse {
            return responseProvider(request)
        }
        
        override suspend fun streamChat(request: AiRequest): kotlinx.coroutines.flow.Flow<AiStreamChunk> {
            throw NotImplementedError("Stream chat not implemented for test")
        }
        
        override suspend fun testConnection(): <PERSON><PERSON><PERSON> {
            return true
        }
    }

    @Test
    fun `should handle JSON with boolean false value correctly`() = runBlocking {
        val customMockService = CustomMockAiService(mockAiService.config) { request ->
            val response = """
            {
              "action": "EXECUTE_TOOL",
              "toolName": "read-terminal",
              "parameters": {
                "only_selected": false
              },
              "reasoning": "Run tests to verify that they pass.",
              "confidence": 0.9
            }
            """.trimIndent()
            
            AiResponse(
                content = response,
                model = request.model,
                usage = TokenUsage(10, 20, 30),
                finishReason = FinishReason.STOP
            )
        }

        val engine = DefaultAiPromptEngine(customMockService)
        val context = ExecutionContext(
            workingDirectory = "/test",
            projectPath = "/test",
            language = "kotlin",
            framework = "spring-boot",
            buildTool = "gradle"
        )

        val decision = engine.decideNextAction(
            requirement = "Run tests",
            executionHistory = emptyList(),
            availableTools = listOf(
                ToolMetadata(
                    name = "read-terminal",
                    description = "Read terminal output",
                    parameters = listOf(
                        ToolParameter("only_selected", "boolean", "Read only selected text", false)
                    )
                )
            ),
            context = context
        )

        assertTrue(decision is ActionDecision.ExecuteTool)
        val executeTool = decision as ActionDecision.ExecuteTool
        assertEquals("read-terminal", executeTool.toolName)
        assertEquals("false", executeTool.parameters["only_selected"])
    }

    @Test
    fun `should handle JSON with boolean true value correctly`() = runBlocking {
        val customMockService = CustomMockAiService(mockAiService.config) { request ->
            val response = """
            {
              "action": "EXECUTE_TOOL",
              "toolName": "read-terminal",
              "parameters": {
                "only_selected": true
              },
              "reasoning": "Read only selected text from terminal.",
              "confidence": 0.9
            }
            """.trimIndent()
            
            AiResponse(
                content = response,
                model = request.model,
                usage = TokenUsage(10, 20, 30),
                finishReason = FinishReason.STOP
            )
        }

        val engine = DefaultAiPromptEngine(customMockService)
        val context = ExecutionContext(
            workingDirectory = "/test",
            projectPath = "/test",
            language = "kotlin",
            framework = "spring-boot",
            buildTool = "gradle"
        )

        val decision = engine.decideNextAction(
            requirement = "Read selected terminal text",
            executionHistory = emptyList(),
            availableTools = listOf(
                ToolMetadata(
                    name = "read-terminal",
                    description = "Read terminal output",
                    parameters = listOf(
                        ToolParameter("only_selected", "boolean", "Read only selected text", false)
                    )
                )
            ),
            context = context
        )

        assertTrue(decision is ActionDecision.ExecuteTool)
        val executeTool = decision as ActionDecision.ExecuteTool
        assertEquals("read-terminal", executeTool.toolName)
        assertEquals("true", executeTool.parameters["only_selected"])
    }

    @Test
    fun `should handle JSON with numeric values correctly`() = runBlocking {
        val customMockService = CustomMockAiService(mockAiService.config) { request ->
            val response = """
            {
              "action": "EXECUTE_TOOL",
              "toolName": "test-tool",
              "parameters": {
                "count": 42,
                "ratio": 3.14
              },
              "reasoning": "Test numeric values.",
              "confidence": 0.95
            }
            """.trimIndent()
            
            AiResponse(
                content = response,
                model = request.model,
                usage = TokenUsage(10, 20, 30),
                finishReason = FinishReason.STOP
            )
        }

        val engine = DefaultAiPromptEngine(customMockService)
        val context = ExecutionContext(
            workingDirectory = "/test",
            projectPath = "/test",
            language = "kotlin",
            framework = "spring-boot",
            buildTool = "gradle"
        )

        val decision = engine.decideNextAction(
            requirement = "Test numeric values",
            executionHistory = emptyList(),
            availableTools = listOf(
                ToolMetadata(
                    name = "test-tool",
                    description = "Test tool",
                    parameters = listOf(
                        ToolParameter("count", "number", "Count parameter", true),
                        ToolParameter("ratio", "number", "Ratio parameter", true)
                    )
                )
            ),
            context = context
        )

        assertTrue(decision is ActionDecision.ExecuteTool)
        val executeTool = decision as ActionDecision.ExecuteTool
        assertEquals("test-tool", executeTool.toolName)
        assertEquals("42", executeTool.parameters["count"])
        assertEquals("3.14", executeTool.parameters["ratio"])
    }
}
