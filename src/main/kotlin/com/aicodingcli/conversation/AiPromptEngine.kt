package com.aicodingcli.conversation

import com.aicodingcli.ai.AiService
import com.aicodingcli.ai.AiRequest
import com.aicodingcli.ai.AiMessage
import com.aicodingcli.ai.MessageRole
import com.aicodingcli.debug.DebugManager
import com.aicodingcli.interaction.UserInteractionManager
import com.aicodingcli.interaction.UserAction
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/**
 * AI Prompt Engine for driving automatic tool calls
 */
interface AiPromptEngine {
    /**
     * Analyze requirement and suggest next actions
     */
    suspend fun analyzeRequirement(requirement: String, context: ExecutionContext): AnalysisResult
    
    /**
     * Decide next tool call based on current state
     */
    suspend fun decideNextAction(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        availableTools: List<ToolMetadata>,
        context: ExecutionContext
    ): ActionDecision
    
    /**
     * Evaluate if the requirement has been completed
     */
    suspend fun evaluateCompletion(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        context: ExecutionContext
    ): CompletionEvaluation
}

/**
 * Default implementation of AI Prompt Engine with conversation history support
 */
class DefaultAiPromptEngine(
    private val aiService: AiService,
    private val userInteractionManager: UserInteractionManager? = null,
    private val json: Json = Json { ignoreUnknownKeys = true }
) : AiPromptEngine {

    private val conversationHistory = ConversationHistoryManager()
    private var isInitialized = false
    
    override suspend fun analyzeRequirement(requirement: String, context: ExecutionContext): AnalysisResult {
        val prompt = buildAnalysisPrompt(requirement, context)

        try {
            val request = AiRequest(
                messages = listOf(AiMessage(MessageRole.USER, prompt)),
                model = aiService.config.model,
                temperature = 0.3f, // Lower temperature for analysis
                maxTokens = aiService.config.maxTokens
            )

            DebugManager.logAiRequest(request, "Requirement Analysis")
            val response = aiService.chat(request)
            DebugManager.logAiResponse(response, "Requirement Analysis")

            return parseAnalysisResponse(response.content)
        } catch (e: Exception) {
            DebugManager.error("Failed to analyze requirement: ${e.message}")
            return AnalysisResult.failure("Failed to analyze requirement: ${e.message}")
        }
    }
    
    override suspend fun decideNextAction(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        availableTools: List<ToolMetadata>,
        context: ExecutionContext
    ): ActionDecision {
        // Initialize conversation on first call
        if (!isInitialized) {
            initializeConversation(requirement, context, availableTools)
            isInitialized = true
        }

        // Add tool results from execution history to conversation
        addNewToolResults(executionHistory)

        // Add action request message
        val actionRequestMessage = ConversationHistoryBuilder.buildActionRequestMessage()
        conversationHistory.addUserMessage(actionRequestMessage)

        try {
            val request = AiRequest(
                messages = conversationHistory.getRecentMessages(maxMessages = 20),
                model = aiService.config.model,
                temperature = 0.2f, // Lower temperature for action decisions
                maxTokens = aiService.config.maxTokens
            )

            DebugManager.logAiRequest(request, "Action Decision")
            val response = aiService.chat(request)
            DebugManager.logAiResponse(response, "Action Decision")

            // Add AI response to conversation history
            conversationHistory.addAssistantMessage(response.content)

            val actionDecision = parseActionDecisionResponse(response.content)

            // Handle WAIT_USER action with actual user interaction
            if (actionDecision is ActionDecision.WaitUser && userInteractionManager != null) {
                return handleUserInteraction(actionDecision, requirement, executionHistory, context)
            }

            return actionDecision
        } catch (e: Exception) {
            DebugManager.error("Failed to decide next action: ${e.message}")
            return ActionDecision.failure("Failed to decide next action: ${e.message}")
        }
    }
    
    override suspend fun evaluateCompletion(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        context: ExecutionContext
    ): CompletionEvaluation {
        val prompt = buildCompletionEvaluationPrompt(requirement, executionHistory, context)

        try {
            val request = AiRequest(
                messages = listOf(AiMessage(MessageRole.USER, prompt)),
                model = aiService.config.model,
                temperature = 0.1f, // Lowest temperature for completion evaluation
                maxTokens = aiService.config.maxTokens
            )

            DebugManager.logAiRequest(request, "Completion Evaluation")
            val response = aiService.chat(request)
            DebugManager.logAiResponse(response, "Completion Evaluation")

            return parseCompletionEvaluationResponse(response.content)
        } catch (e: Exception) {
            DebugManager.error("Failed to evaluate completion: ${e.message}")
            return CompletionEvaluation.failure("Failed to evaluate completion: ${e.message}")
        }
    }
    
    private fun buildAnalysisPrompt(requirement: String, context: ExecutionContext): String {
        // Get current system information
        val currentTime = java.time.LocalDateTime.now().toString()
        val workingDir = context.workingDirectory
        val projectPath = if (context.projectPath.isBlank()) workingDir else context.projectPath

        // Get directory structure
        val directoryStructure = try {
            val dir = java.io.File(projectPath)
            if (dir.exists() && dir.isDirectory()) {
                dir.listFiles()?.take(10)?.joinToString("\n") { file ->
                    val type = if (file.isDirectory()) "[DIR]" else "[FILE]"
                    "  $type ${file.name}"
                } ?: "Empty directory"
            } else {
                "Directory does not exist or is not accessible"
            }
        } catch (e: Exception) {
            "Unable to read directory structure: ${e.message}"
        }

        return """
            You are an AI assistant that analyzes programming requirements and breaks them down into actionable steps.

            **Requirement**: $requirement

            **Current System Environment**:
            - Current Time: $currentTime
            - Working Directory: $workingDir
            - Project Path: $projectPath
            - Language: ${context.language}
            - Framework: ${context.framework}
            - Build Tool: ${context.buildTool}

            **Current Directory Structure** (${projectPath}):
            $directoryStructure

            Please analyze this requirement considering the current environment and provide:
            1. **Intent**: What is the user trying to achieve?
            2. **Complexity**: How complex is this requirement? (SIMPLE, MODERATE, COMPLEX)
            3. **Category**: What category does this fall into? (FILE_OPERATION, CODE_GENERATION, TESTING, REFACTORING, ANALYSIS, OTHER)
            4. **Prerequisites**: What needs to exist before this can be completed?
            5. **Estimated Steps**: How many steps do you estimate this will take?
            6. **File Strategy**: Where should files be created based on the current directory structure?

            Respond in JSON format:
            {
                "intent": "string",
                "complexity": "SIMPLE|MODERATE|COMPLEX",
                "category": "FILE_OPERATION|CODE_GENERATION|TESTING|REFACTORING|ANALYSIS|OTHER",
                "prerequisites": ["string"],
                "estimatedSteps": number,
                "reasoning": "string explaining your analysis and file placement strategy"
            }
        """.trimIndent()
    }
    
    private fun buildActionDecisionPrompt(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        availableTools: List<ToolMetadata>,
        context: ExecutionContext
    ): String {
        val historyText = if (executionHistory.isEmpty()) {
            "No previous actions taken."
        } else {
            executionHistory.takeLast(5).joinToString("\n") { step ->
                "- ${step.toolCall.toolName}: ${step.result.success} - ${step.result.output.take(100)}"
            }
        }

        val toolsText = availableTools.joinToString("\n") { tool ->
            "- ${tool.name}: ${tool.description}"
        }

        // Get current system information
        val currentTime = java.time.LocalDateTime.now().toString()
        val workingDir = context.workingDirectory
        val projectPath = if (context.projectPath.isBlank()) workingDir else context.projectPath

        // Get directory structure
        val directoryStructure = try {
            val dir = java.io.File(projectPath)
            if (dir.exists() && dir.isDirectory()) {
                dir.listFiles()?.take(10)?.joinToString("\n") { file ->
                    val type = if (file.isDirectory()) "[DIR]" else "[FILE]"
                    "  $type ${file.name}"
                } ?: "Empty directory"
            } else {
                "Directory does not exist or is not accessible"
            }
        } catch (e: Exception) {
            "Unable to read directory structure: ${e.message}"
        }

        return """
            You are an AI assistant that decides the next action to take in completing a programming requirement.

            **Original Requirement**: $requirement

            **Current System Environment**:
            - Current Time: $currentTime
            - Working Directory: $workingDir
            - Project Path: $projectPath
            - Language: ${context.language}
            - Framework: ${context.framework}
            - Build Tool: ${context.buildTool}

            **Current Directory Structure** (${projectPath}):
            $directoryStructure

            **Execution History** (last 5 actions):
            $historyText

            **Available Tools**:
            $toolsText

            **Instructions**:
            Based on the requirement, current environment, and execution history, decide the next action to take.

            **File Creation Guidelines**:
            - For simple requirements, create files directly in the working directory
            - For complex projects, consider creating appropriate subdirectories
            - Use appropriate file extensions (.kt for Kotlin, .java for Java, etc.)
            - Include proper package declarations and imports when needed

            **Decision Guidelines**:
            - If the requirement seems complete, use action "COMPLETE"
            - If you need user input or clarification, use action "WAIT_USER"
            - If something went wrong and cannot be fixed, use action "FAIL"
            - Otherwise, use action "EXECUTE_TOOL" with the appropriate tool and parameters
            - Always check current directory structure before creating files
            - Prefer using 'view' tool to understand current state before making changes

            Respond in JSON format:
            {
                "action": "EXECUTE_TOOL|COMPLETE|WAIT_USER|FAIL",
                "toolName": "string (if action is EXECUTE_TOOL)",
                "parameters": {"key": "value"} (if action is EXECUTE_TOOL),
                "reasoning": "string explaining your decision and file placement strategy",
                "confidence": number (0.0 to 1.0)
            }
        """.trimIndent()
    }
    
    private fun buildCompletionEvaluationPrompt(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        context: ExecutionContext
    ): String {
        val historyText = executionHistory.joinToString("\n") { step ->
            "- ${step.toolCall.toolName}: ${if (step.result.success) "SUCCESS" else "FAILED"} - ${step.result.output.take(100)}"
        }

        // Get current system information
        val currentTime = java.time.LocalDateTime.now().toString()
        val workingDir = context.workingDirectory
        val projectPath = if (context.projectPath.isBlank()) workingDir else context.projectPath

        // Get current directory structure to verify what was created
        val directoryStructure = try {
            val dir = java.io.File(projectPath)
            if (dir.exists() && dir.isDirectory()) {
                dir.listFiles()?.joinToString("\n") { file ->
                    val type = if (file.isDirectory()) "[DIR]" else "[FILE]"
                    val size = if (file.isFile()) " (${file.length()} bytes)" else ""
                    "  $type ${file.name}$size"
                } ?: "Empty directory"
            } else {
                "Directory does not exist or is not accessible"
            }
        } catch (e: Exception) {
            "Unable to read directory structure: ${e.message}"
        }

        return """
            You are an AI assistant that evaluates whether a programming requirement has been completed.

            **Original Requirement**: $requirement

            **Current System Environment**:
            - Current Time: $currentTime
            - Working Directory: $workingDir
            - Project Path: $projectPath
            - Language: ${context.language}
            - Framework: ${context.framework}
            - Build Tool: ${context.buildTool}

            **Current Directory Structure** (${projectPath}):
            $directoryStructure

            **Execution History**:
            $historyText

            Evaluate whether the original requirement has been successfully completed by examining:
            1. The execution history and what actions were taken
            2. The current directory structure and files that were created
            3. Whether the created files match the requirement

            Respond in JSON format:
            {
                "completed": boolean,
                "completionPercentage": number (0 to 100),
                "missingItems": ["string"],
                "summary": "string describing what was accomplished",
                "nextSteps": ["string"] (if not completed)
            }
        """.trimIndent()
    }
    
    private fun parseAnalysisResponse(response: String): AnalysisResult {
        return try {
            val extractedJson = extractJsonFromResponse(response)
            val jsonResponse = json.decodeFromString<AnalysisResponseJson>(extractedJson.jsonContent)

            // Include any non-JSON text in the reasoning
            val fullReasoning = if (extractedJson.nonJsonText.isNotBlank()) {
                "${extractedJson.nonJsonText}\n\n${jsonResponse.reasoning}"
            } else {
                jsonResponse.reasoning
            }

            AnalysisResult.success(
                intent = jsonResponse.intent,
                complexity = RequirementComplexity.valueOf(jsonResponse.complexity),
                category = RequirementCategory.valueOf(jsonResponse.category),
                prerequisites = jsonResponse.prerequisites,
                estimatedSteps = jsonResponse.estimatedSteps,
                reasoning = fullReasoning
            )
        } catch (e: Exception) {
            DebugManager.error("Failed to parse analysis response: ${e.message}")
            DebugManager.error("Raw response: $response")
            AnalysisResult.failure("Failed to parse analysis response: ${e.message}\nRaw input: $response")
        }
    }
    
    private fun parseActionDecisionResponse(response: String): ActionDecision {
        return try {
            val extractedJson = extractJsonFromResponse(response)
            val jsonResponse = json.decodeFromString<ActionDecisionResponseJson>(extractedJson.jsonContent)

            // Include any non-JSON text in the reasoning
            val fullReasoning = if (extractedJson.nonJsonText.isNotBlank()) {
                "${extractedJson.nonJsonText}\n\n${jsonResponse.reasoning}"
            } else {
                jsonResponse.reasoning
            }

            when (jsonResponse.action) {
                "EXECUTE_TOOL" -> ActionDecision.executeTool(
                    toolName = jsonResponse.toolName ?: "",
                    parameters = convertJsonElementsToStrings(jsonResponse.parameters ?: emptyMap()),
                    reasoning = fullReasoning,
                    confidence = jsonResponse.confidence
                )
                "COMPLETE" -> ActionDecision.complete(fullReasoning)
                "WAIT_USER" -> ActionDecision.waitUser(fullReasoning)
                "FAIL" -> ActionDecision.fail(fullReasoning)
                else -> ActionDecision.failure("Unknown action: ${jsonResponse.action}")
            }
        } catch (e: Exception) {
            DebugManager.error("Failed to parse action decision response: ${e.message}")
            DebugManager.error("Raw response: $response")
            ActionDecision.failure("Failed to parse action decision response: ${e.message}\nJSON input: $response")
        }
    }
    
    private fun parseCompletionEvaluationResponse(response: String): CompletionEvaluation {
        return try {
            val extractedJson = extractJsonFromResponse(response)
            val jsonResponse = json.decodeFromString<CompletionEvaluationResponseJson>(extractedJson.jsonContent)

            // Include any non-JSON text in the summary
            val fullSummary = if (extractedJson.nonJsonText.isNotBlank()) {
                "${extractedJson.nonJsonText}\n\n${jsonResponse.summary}"
            } else {
                jsonResponse.summary
            }

            CompletionEvaluation.success(
                completed = jsonResponse.completed,
                completionPercentage = jsonResponse.completionPercentage,
                missingItems = jsonResponse.missingItems,
                summary = fullSummary,
                nextSteps = jsonResponse.nextSteps
            )
        } catch (e: Exception) {
            DebugManager.error("Failed to parse completion evaluation response: ${e.message}")
            DebugManager.error("Raw response: $response")
            CompletionEvaluation.failure("Failed to parse completion evaluation response: ${e.message}\nRaw input: $response")
        }
    }

    /**
     * Handle user interaction when AI requests user input
     */
    private suspend fun handleUserInteraction(
        waitUserDecision: ActionDecision.WaitUser,
        requirement: String,
        executionHistory: List<ExecutionStep>,
        context: ExecutionContext
    ): ActionDecision {
        if (userInteractionManager == null) {
            DebugManager.warning("User interaction requested but no interaction manager available")
            return waitUserDecision
        }

        DebugManager.info("AI is requesting user input: ${waitUserDecision.reasoning}")

        // Display the AI's reasoning and ask for user input
        val actions = listOf(
            UserAction.continueAction("Provide the requested information"),
            UserAction.modify("Modify the requirement"),
            UserAction.cancel("Cancel the operation")
        )

        val selectedAction = userInteractionManager.displayActionPrompt(
            title = "AI Needs Your Input",
            message = waitUserDecision.reasoning,
            actions = actions
        )

        return when (selectedAction.id) {
            "continue" -> {
                // Get user input based on the AI's request
                val userInput = getUserInputBasedOnReasoning(waitUserDecision.reasoning)

                // Create a new prompt with user input and ask AI to decide again
                val updatedPrompt = buildActionDecisionPromptWithUserInput(
                    requirement, executionHistory, context, waitUserDecision.reasoning, userInput
                )

                try {
                    val request = AiRequest(
                        messages = listOf(AiMessage(MessageRole.USER, updatedPrompt)),
                        model = aiService.config.model,
                        temperature = aiService.config.temperature,
                        maxTokens = aiService.config.maxTokens
                    )

                    DebugManager.logAiRequest(request, "Action Decision with User Input")
                    val response = aiService.chat(request)
                    DebugManager.logAiResponse(response, "Action Decision with User Input")

                    parseActionDecisionResponse(response.content)
                } catch (e: Exception) {
                    DebugManager.error("Failed to process user input: ${e.message}")
                    ActionDecision.failure("Failed to process user input: ${e.message}")
                }
            }
            "modify" -> {
                // Allow user to modify the requirement
                val newRequirement = userInteractionManager.promptUser(
                    "Please provide the modified requirement",
                    requirement
                )

                DebugManager.info("User modified requirement to: $newRequirement")

                // Restart the decision process with new requirement
                decideNextAction(newRequirement, executionHistory, emptyList(), context)
            }
            "cancel" -> {
                DebugManager.info("User cancelled the operation")
                ActionDecision.fail("Operation cancelled by user")
            }
            else -> {
                DebugManager.warning("Unknown user action: ${selectedAction.id}")
                waitUserDecision
            }
        }
    }

    /**
     * Get user input based on AI's reasoning
     */
    private fun getUserInputBasedOnReasoning(reasoning: String): String {
        return when {
            reasoning.contains("file path", ignoreCase = true) ||
            reasoning.contains("where", ignoreCase = true) -> {
                userInteractionManager?.promptFilePath(
                    "Please specify the file path",
                    mustExist = false,
                    defaultValue = "src/main/kotlin/"
                ) ?: ""
            }
            reasoning.contains("choice", ignoreCase = true) ||
            reasoning.contains("option", ignoreCase = true) -> {
                // Extract options from reasoning if possible, or ask for general choice
                userInteractionManager?.promptUser(
                    "Please provide your choice or preference",
                    ""
                ) ?: ""
            }
            reasoning.contains("confirm", ignoreCase = true) -> {
                val confirmed = userInteractionManager?.promptConfirmation(
                    "Do you want to proceed?",
                    defaultValue = true
                ) ?: true
                if (confirmed) "yes" else "no"
            }
            else -> {
                // General text input
                userInteractionManager?.promptUser(
                    "Please provide the requested information",
                    ""
                ) ?: ""
            }
        }
    }

    /**
     * Build action decision prompt with user input
     */
    private fun buildActionDecisionPromptWithUserInput(
        requirement: String,
        executionHistory: List<ExecutionStep>,
        context: ExecutionContext,
        aiQuestion: String,
        userAnswer: String
    ): String {
        val basePrompt = buildActionDecisionPrompt(requirement, executionHistory, emptyList(), context)

        return """
            $basePrompt

            **Previous AI Question**: $aiQuestion
            **User Answer**: $userAnswer

            Based on the user's input, please decide the next action to take.
        """.trimIndent()
    }

    /**
     * Initialize conversation with system prompt
     */
    private fun initializeConversation(
        requirement: String,
        context: ExecutionContext,
        availableTools: List<ToolMetadata>
    ) {
        val systemPrompt = ConversationHistoryBuilder.buildInitialSystemPrompt(
            requirement, context, availableTools
        )
        conversationHistory.setSystemPrompt(systemPrompt)
        DebugManager.debug("Conversation initialized with system prompt")
    }

    /**
     * Add new tool results to conversation history
     */
    private var lastProcessedStepCount = 0

    private fun addNewToolResults(executionHistory: List<ExecutionStep>) {
        // Clear previous tool results and add only the last 5 steps
        conversationHistory.clearToolResults()

        val recentSteps = executionHistory.takeLast(5)
        recentSteps.forEach { step ->
            conversationHistory.addToolResult(step.toolCall.toolName, step.result)
        }

        lastProcessedStepCount = executionHistory.size

        if (recentSteps.isNotEmpty()) {
            DebugManager.debug("Added ${recentSteps.size} tool results to conversation (last 5 of ${executionHistory.size})")
        }
    }

    /**
     * Reset conversation for new session
     */
    fun resetConversation() {
        conversationHistory.clearAll()
        isInitialized = false
        lastProcessedStepCount = 0
        DebugManager.debug("Conversation reset for new session")
    }

    /**
     * Extract JSON content from AI response that may contain non-JSON text
     */
    private fun extractJsonFromResponse(response: String): JsonExtractionResult {
        var trimmedResponse = response.trim()

        // Remove markdown code block markers if present
        if (trimmedResponse.startsWith("```json")) {
            trimmedResponse = trimmedResponse.removePrefix("```json").trim()
        }
        if (trimmedResponse.startsWith("```")) {
            trimmedResponse = trimmedResponse.removePrefix("```").trim()
        }
        if (trimmedResponse.endsWith("```")) {
            trimmedResponse = trimmedResponse.removeSuffix("```").trim()
        }

        // Try to find JSON object boundaries
        val jsonStart = trimmedResponse.indexOf('{')
        val jsonEnd = trimmedResponse.lastIndexOf('}')

        if (jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd) {
            // No valid JSON found, return the whole response as non-JSON text
            return JsonExtractionResult(
                jsonContent = "{}",
                nonJsonText = trimmedResponse
            )
        }

        // Extract JSON content
        var jsonContent = trimmedResponse.substring(jsonStart, jsonEnd + 1)

        // Fix common JSON issues from AI responses
        jsonContent = fixJsonIssues(jsonContent)

        // Extract non-JSON text (before and after JSON)
        val beforeJson = trimmedResponse.substring(0, jsonStart).trim()
        val afterJson = trimmedResponse.substring(jsonEnd + 1).trim()
        val nonJsonText = listOf(beforeJson, afterJson)
            .filter { it.isNotBlank() }
            .joinToString("\n\n")

        return JsonExtractionResult(
            jsonContent = jsonContent,
            nonJsonText = nonJsonText
        )
    }

    /**
     * Fix common JSON formatting issues from AI responses
     */
    private fun fixJsonIssues(jsonContent: String): String {
        var fixed = jsonContent

        // Fix triple quotes (""") to escaped quotes
        // This handles cases where AI uses """ for multi-line strings
        fixed = fixed.replace("\"\"\"", "\"")

        // Only fix newlines inside string values, not in the JSON structure
        // Use a more careful approach to preserve JSON formatting
        fixed = fixNewlinesInStringValues(fixed)

        return fixed
    }

    /**
     * Fix newlines only within JSON string values, preserving JSON structure
     */
    private fun fixNewlinesInStringValues(jsonContent: String): String {
        val result = StringBuilder()
        var inString = false
        var escapeNext = false
        var i = 0

        while (i < jsonContent.length) {
            val char = jsonContent[i]

            when {
                escapeNext -> {
                    result.append(char)
                    escapeNext = false
                }
                char == '\\' -> {
                    result.append(char)
                    escapeNext = true
                }
                char == '"' -> {
                    result.append(char)
                    inString = !inString
                }
                inString && char == '\n' -> {
                    result.append("\\n")
                }
                inString && char == '\r' -> {
                    result.append("\\r")
                }
                inString && char == '\t' -> {
                    result.append("\\t")
                }
                else -> {
                    result.append(char)
                }
            }
            i++
        }

        return result.toString()
    }

    /**
     * Convert JsonElement map to String map for tool parameters
     */
    private fun convertJsonElementsToStrings(jsonMap: Map<String, kotlinx.serialization.json.JsonElement>): Map<String, String> {
        return jsonMap.mapValues { (_, value) ->
            when (value) {
                is kotlinx.serialization.json.JsonPrimitive -> {
                    if (value.isString) {
                        value.content
                    } else {
                        // For numbers, booleans, etc., convert to string representation
                        value.content
                    }
                }
                else -> value.toString()
            }
        }
    }
}

/**
 * Result of JSON extraction from AI response
 */
private data class JsonExtractionResult(
    val jsonContent: String,
    val nonJsonText: String
)

// JSON response data classes
@Serializable
private data class AnalysisResponseJson(
    val intent: String,
    val complexity: String,
    val category: String,
    val prerequisites: List<String>,
    val estimatedSteps: Int,
    val reasoning: String
)

@Serializable
private data class ActionDecisionResponseJson(
    val action: String,
    val toolName: String? = null,
    val parameters: Map<String, kotlinx.serialization.json.JsonElement>? = null,
    val reasoning: String,
    val confidence: Double
)

@Serializable
private data class CompletionEvaluationResponseJson(
    val completed: Boolean,
    val completionPercentage: Int,
    val missingItems: List<String>,
    val summary: String,
    val nextSteps: List<String>
)
